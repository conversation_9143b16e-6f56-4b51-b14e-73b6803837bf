"""
Python Print Function Tutorial
=============================

This file demonstrates various ways to use the print() function in Python.
The print() function is used to display output to the console.

Key concepts covered:
- Basic syntax and usage
- Printing different data types
- Using parameters like sep and end
- String formatting techniques
- Practical applications
"""

# =============================================================================
# 1. BASIC PRINT USAGE
# =============================================================================
# The simplest form of print() - just pass a string as an argument
print("Hello, World!")  # Output: Hello, World!
print("Welcome to Python!")  # Output: Welcome to Python!

# =============================================================================
# 2. PRINTING DIFFERENT DATA TYPES
# =============================================================================
# Python's print() function can handle any data type automatically
print(42)           # Integer - Output: 42
print(3.14)         # Float (decimal number) - Output: 3.14
print(True)         # Boolean - Output: True
print([1, 2, 3])    # List - Output: [1, 2, 3]

# Note: Python automatically converts these to strings for display

# =============================================================================
# 3. PRINTING MULTIPLE ITEMS
# =============================================================================
# You can pass multiple arguments to print(), separated by commas
# By default, they are separated by a single space
print("Name:", "Alice", "Age:", 25)  # Output: Name: Alice Age: 25
print("The answer is", 42)           # Output: The answer is 42

# =============================================================================
# 4. USING VARIABLES
# =============================================================================
# Variables can be used directly in print statements
name = "Bob"        # String variable
age = 30           # Integer variable
print("Hello, my name is", name)    # Output: Hello, my name is Bob
print("I am", age, "years old")     # Output: I am 30 years old

# =============================================================================
# 5. PRINT WITH CUSTOM SEPARATORS
# =============================================================================
# The 'sep' parameter controls what goes between multiple arguments
# Default separator is a single space (' ')
print("apple", "banana", "cherry", sep=", ")    # Output: apple, banana, cherry
print("2024", "12", "25", sep="-")              # Output: 2024-12-25
print("A", "B", "C", sep=" | ")                 # Output: A | B | C

# =============================================================================
# 6. PRINT WITH CUSTOM END CHARACTER
# =============================================================================
# The 'end' parameter controls what comes after the printed content
# Default end character is newline ('\n')
print("Loading", end="...")  # Output: Loading... (no newline)
print("Done!")               # Output: Done! (appears on same line as "Loading...")
print("Line 1", end=" ")     # Output: Line 1  (space instead of newline)
print("Line 2")              # Output: Line 2 (appears on same line)

# =============================================================================
# 7. PRINTING TO DIFFERENT OUTPUTS (ADVANCED)
# =============================================================================
# By default, print() sends output to stdout (standard output)
# You can redirect output using the 'file' parameter
import sys
print("This goes to standard output")                    # Normal output
print("This goes to error output", file=sys.stderr)     # Error output (red in some terminals)

# =============================================================================
# 8. FORMATTED STRINGS (F-STRINGS) - MODERN PYTHON WAY
# =============================================================================
# F-strings (formatted string literals) are the most readable way to format strings
# Put 'f' before the string and use {} to embed expressions
name = "Charlie"
score = 95.5
print(f"Student: {name}, Score: {score}%")    # Output: Student: Charlie, Score: 95.5%
print(f"Calculation: 10 + 5 = {10 + 5}")      # Output: Calculation: 10 + 5 = 15

# F-strings can include any Python expression inside the {}
print(f"Uppercase name: {name.upper()}")      # Output: Uppercase name: CHARLIE
print(f"Score rounded: {round(score)}")       # Output: Score rounded: 96

# =============================================================================
# 9. STRING FORMATTING WITH .format() METHOD
# =============================================================================
# The .format() method is an older but still useful way to format strings
# Use {} as placeholders and pass values to .format()
template = "Hello {}, you have {} messages"
print(template.format("David", 3))            # Output: Hello David, you have 3 messages

# You can control number formatting with format specifiers
print("Pi is approximately {:.2f}".format(3.14159))  # Output: Pi is approximately 3.14
print("Large number: {:,}".format(1234567))          # Output: Large number: 1,234,567

# =============================================================================
# 10. ESCAPE CHARACTERS
# =============================================================================
# Escape characters start with backslash (\) and represent special characters
print("This is a \"quoted\" word")           # \" represents a quote mark
print("Line 1\nLine 2")                     # \n creates a new line
print("Column1\tColumn2")                   # \t creates a tab space

# =============================================================================
# 11. PRINTING SPECIAL CHARACTERS
# =============================================================================
# Use double backslash (\\) to print a literal backslash
print("Path: C:\\Users\\<USER>\Users\Documents

# Unicode characters can be printed using \u followed by the code
print("Unicode: \u2764 \u2665")            # Output: Unicode: ❤ ♥ (heart symbols)
print("Math symbols: \u03C0 \u221E")       # Output: Math symbols: π ∞ (pi and infinity)

# =============================================================================
# 12. PRINT FUNCTION WITH NO ARGUMENTS
# =============================================================================
# Calling print() without arguments creates a blank line
print()  # Prints an empty line - useful for spacing output

# =============================================================================
# 13. PRACTICAL EXAMPLES
# =============================================================================
# Creating visual separators and menus using print()
print("=" * 40)  # Multiply string to create a line of 40 equal signs
print("MENU")
print("=" * 40)
print("1. Option A")
print("2. Option B")
print("3. Exit")
print("=" * 40)

# Creating formatted tables
print("\nStudent Grades:")
print("-" * 30)
print(f"{'Name':<15} {'Grade':<10}")  # Left-aligned formatting
print("-" * 30)
print(f"{'Alice':<15} {'A+':<10}")
print(f"{'Bob':<15} {'B':<10}")
print(f"{'Charlie':<15} {'A-':<10}")

# Progress indicators
print("\nProgress:")
for i in range(1, 6):
    print("█" * i + "░" * (5-i), f"{i*20}%")

# =============================================================================
# 14. DEBUGGING WITH PRINT
# =============================================================================
# Print statements are excellent for debugging - showing variable values
# and program flow during development
def calculate_area(length, width):
    """
    Calculate the area of a rectangle with debug output
    """
    print(f"Debug: Entering function with length={length}, width={width}")

    # Validate inputs
    if length <= 0 or width <= 0:
        print("Debug: Invalid input detected!")
        return None

    result = length * width
    print(f"Debug: Calculated area = {length} × {width} = {result}")
    return result

# Test the function with debug output
print("\n" + "="*50)
print("DEBUGGING EXAMPLE")
print("="*50)
area = calculate_area(5, 3)
print(f"Final result: {area} square units")

# Example of debugging a loop
print("\nDebugging a loop:")
total = 0
for i in range(1, 4):
    total += i
    print(f"Debug: i={i}, total so far={total}")  # Track loop progress
print(f"Final total: {total}")

# =============================================================================
# 15. ADVANCED TIPS AND BEST PRACTICES
# =============================================================================
# Tip 1: Use print() for temporary debugging, but remove before production
# Tip 2: F-strings are generally preferred over .format() for readability
# Tip 3: Use meaningful variable names in your print statements
# Tip 4: Consider using logging module for production applications instead of print()

print("\n" + "="*50)
print("END OF TUTORIAL")
print("="*50)
print("Remember: Practice makes perfect!")
print("Try modifying these examples to learn more.")
