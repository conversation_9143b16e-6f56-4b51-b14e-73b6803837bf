print("Hello, <PERSON>!")
print("Welcome to Python!")

print(42)
print(3.14)
print(True)
print([1, 2, 3])

print("Name:", "<PERSON>", "Age:", 25)
print("The answer is", 42)

name = "<PERSON>"
age = 30
print("Hello, my name is", name)
print("I am", age, "years old")

print("apple", "banana", "cherry", sep=", ")
print("2024", "12", "25", sep="-")
print("A", "B", "C", sep=" | ")

print("Loading", end="...")
print("Done!")
print("Line 1", end=" ")
print("Line 2")

import sys
print("This goes to standard output")
print("This goes to error output", file=sys.stderr)

name = "Charlie"
score = 95.5
print(f"Student: {name}, Score: {score}%")
print(f"Calculation: 10 + 5 = {10 + 5}")
print(f"Uppercase name: {name.upper()}")
print(f"Score rounded: {round(score)}")

template = "Hello {}, you have {} messages"
print(template.format("David", 3))
print("Pi is approximately {:.2f}".format(3.14159))
print("Large number: {:,}".format(1234567))

print("This is a \"quoted\" word")
print("Line 1\nLine 2")
print("Column1\tColumn2")

print("Path: C:\\Users\\<USER>\u2764 \u2665")
print("Math symbols: \u03C0 \u221E")

print()

print("=" * 40)
print("MENU")
print("=" * 40)
print("1. Option A")
print("2. Option B")
print("3. Exit")
print("=" * 40)

print("\nStudent Grades:")
print("-" * 30)
print(f"{'Name':<15} {'Grade':<10}")
print("-" * 30)
print(f"{'Alice':<15} {'A+':<10}")
print(f"{'Bob':<15} {'B':<10}")
print(f"{'Charlie':<15} {'A-':<10}")

print("\nProgress:")
for i in range(1, 6):
    print("█" * i + "░" * (5-i), f"{i*20}%")

def calculate_area(length, width):
    print(f"Debug: Entering function with length={length}, width={width}")
    if length <= 0 or width <= 0:
        print("Debug: Invalid input detected!")
        return None
    result = length * width
    print(f"Debug: Calculated area = {length} × {width} = {result}")
    return result

print("\n" + "="*50)
print("DEBUGGING EXAMPLE")
print("="*50)
area = calculate_area(5, 3)
print(f"Final result: {area} square units")

print("\nDebugging a loop:")
total = 0
for i in range(1, 4):
    total += i
    print(f"Debug: i={i}, total so far={total}")
print(f"Final total: {total}")

print("\n" + "="*50)
print("END OF TUTORIAL")
print("="*50)
print("Remember: Practice makes perfect!")
print("Try modifying these examples to learn more.")
