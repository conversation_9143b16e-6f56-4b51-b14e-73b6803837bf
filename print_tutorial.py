"""
Python Print Function Tutorial
=============================

This file demonstrates various ways to use the print() function in Python.
The print() function is used to display output to the console.
"""

# 1. Basic print usage
print("Hello, World!")
print("Welcome to Python!")

# 2. Printing different data types
print(42)           # Integer
print(3.14)         # Float
print(True)         # Boolean
print([1, 2, 3])    # List

# 3. Printing multiple items
print("Name:", "Alice", "Age:", 25)
print("The answer is", 42)

# 4. Using variables
name = "<PERSON>"
age = 30
print("Hello, my name is", name)
print("I am", age, "years old")

# 5. Print with separators
print("apple", "banana", "cherry", sep=", ")
print("2024", "12", "25", sep="-")
print("A", "B", "C", sep=" | ")

# 6. Print with custom end character
print("Loading", end="...")
print("Done!")
print("Line 1", end=" ")
print("Line 2")

# 7. Printing to different outputs (advanced)
import sys
print("This goes to standard output")
print("This goes to error output", file=sys.stderr)

# 8. Formatted strings (f-strings) - Modern Python way
name = "Charlie"
score = 95.5
print(f"Student: {name}, Score: {score}%")
print(f"Calculation: 10 + 5 = {10 + 5}")

# 9. String formatting with .format()
template = "Hello {}, you have {} messages"
print(template.format("David", 3))
print("Pi is approximately {:.2f}".format(3.14159))

# 10. Escape characters
print("This is a \"quoted\" word")
print("Line 1\nLine 2")  # \n creates a new line
print("Column1\tColumn2")  # \t creates a tab

# 11. Printing special characters
print("Path: C:\\Users\\<USER>\u2764 \u2665")      # Heart symbols

# 12. Print function with no arguments
print()  # Prints an empty line

# 13. Practical examples
print("=" * 40)  # Print a line of equal signs
print("MENU")
print("=" * 40)
print("1. Option A")
print("2. Option B")
print("3. Exit")
print("=" * 40)

# 14. Debugging with print
def calculate_area(length, width):
    print(f"Debug: length={length}, width={width}")  # Debug output
    result = length * width
    print(f"Debug: calculated area={result}")        # Debug output
    return result

area = calculate_area(5, 3)
print(f"Final result: {area}")
